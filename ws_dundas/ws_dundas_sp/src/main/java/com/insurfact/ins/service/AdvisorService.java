package com.insurfact.ins.service;

import com.insurfact.ins.dto.AdvisorProfileDTO;
import com.insurfact.ins.exception.ResourceNotFoundException;
import com.insurfact.ins.repository.AdvisorProfileRepository;
import com.insurfact.ins.repository.CompanyRepository;
import com.insurfact.ins.mapper.AdvisorMapper;
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Company;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * Service class for managing Advisor operations.
 * Provides business logic for advisor profile management including
 * data retrieval, validation, and transformation.
 *
 * This service acts as the business layer between the REST controllers
 * and the data access layer. It uses the AdvisorProfileRepository to
 * fetch skynet entity objects and the AdvisorMapper to convert them
 * to API DTOs.
 *
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Service
@Transactional(readOnly = true)
public class AdvisorService {

    private static final Logger log = LoggerFactory.getLogger(AdvisorService.class);

    private final AdvisorProfileRepository advisorProfileRepository;
    private final AdvisorMapper advisorMapper;
    private final CompanyRepository companyRepository;

    @Autowired
    public AdvisorService(AdvisorProfileRepository advisorProfileRepository,
                         AdvisorMapper advisorMapper,
                         CompanyRepository companyRepository) {
        this.advisorProfileRepository = advisorProfileRepository;
        this.advisorMapper = advisorMapper;
        this.companyRepository = companyRepository;
    }

    /**
     * Retrieves a complete advisor profile by advisor ID.
     * 
     * This method fetches the advisor's personal information, contact details,
     * addresses, phone numbers, and email addresses in a single operation.
     * The data is returned as a structured DTO suitable for API responses.
     * 
     * @param advisorId the unique identifier of the advisor
     * @return AdvisorProfileDTO containing complete advisor information
     * @throws ResourceNotFoundException if no advisor is found with the given ID
     * @throws IllegalArgumentException if advisorId is null or invalid
     */
    public AdvisorProfileDTO getAdvisorProfile(Long advisorId) {
        log.debug("Retrieving advisor profile for ID: {}", advisorId);

        // Validate input
        if (advisorId == null) {
            throw new IllegalArgumentException("Advisor ID cannot be null");
        }

        if (advisorId <= 0) {
            throw new IllegalArgumentException("Advisor ID must be a positive number");
        }

        try {
            // Fetch advisor entity from repository
            Advisor advisorEntity = advisorProfileRepository.findProfileById(advisorId);

            // Check if advisor was found
            if (advisorEntity == null) {
                log.warn("Advisor not found with ID: {}", advisorId);
                throw new ResourceNotFoundException("Advisor", advisorId);
            }

            // Convert entity to DTO using mapper
            AdvisorProfileDTO advisorProfile = advisorMapper.toAdvisorProfileDTO(advisorEntity);

            // Log successful retrieval
            log.info("Successfully retrieved advisor profile for ID: {} (Code: {})",
                advisorId, advisorProfile.getAdvisorCode());

            // Perform any additional business logic or data enrichment here
            enrichAdvisorProfile(advisorProfile);

            // Fetch and set company information using CompanyFacade
            enrichAdvisorProfileWithCompanies(advisorProfile, advisorEntity);

            return advisorProfile;

        } catch (ResourceNotFoundException e) {
            // Re-throw resource not found exceptions
            throw e;
        } catch (Exception e) {
            // Log and wrap any unexpected exceptions
            log.error("Unexpected error retrieving advisor profile for ID {}: {}", advisorId, e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve advisor profile", e);
        }
    }

    /**
     * Enriches the advisor profile with additional computed or derived information.
     * This method can be extended to add business logic such as:
     * - Computing display names
     * - Adding derived status information
     * - Formatting data for presentation
     * 
     * @param advisorProfile the advisor profile to enrich
     */
    private void enrichAdvisorProfile(AdvisorProfileDTO advisorProfile) {
        // Add any business logic for data enrichment here
        
        // Example: Ensure contact information is properly structured
        if (advisorProfile.getContact() != null) {
            var contact = advisorProfile.getContact();
            
            // Sort addresses to put primary first
            if (contact.getAddresses() != null) {
                contact.getAddresses().sort((a1, a2) -> {
                    if (Boolean.TRUE.equals(a1.getIsPrimary())) return -1;
                    if (Boolean.TRUE.equals(a2.getIsPrimary())) return 1;
                    return 0;
                });
            }
            
            // Sort phones to put primary first
            if (contact.getPhones() != null) {
                contact.getPhones().sort((p1, p2) -> {
                    if (Boolean.TRUE.equals(p1.getIsPrimary())) return -1;
                    if (Boolean.TRUE.equals(p2.getIsPrimary())) return 1;
                    return 0;
                });
            }
            
            // Sort emails to put primary first
            if (contact.getEmails() != null) {
                contact.getEmails().sort((e1, e2) -> {
                    if (Boolean.TRUE.equals(e1.getIsPrimary())) return -1;
                    if (Boolean.TRUE.equals(e2.getIsPrimary())) return 1;
                    return 0;
                });
            }
        }

        log.debug("Advisor profile enrichment completed for ID: {}", advisorProfile.getAdvisorId());
    }

    /**
     * Checks if an advisor exists by ID.
     * This is a lightweight method that can be used for existence checks
     * without fetching the full profile data.
     * 
     * @param advisorId the unique identifier of the advisor
     * @return true if the advisor exists, false otherwise
     */
    public boolean advisorExists(Long advisorId) {
        log.debug("Checking existence of advisor with ID: {}", advisorId);

        if (advisorId == null || advisorId <= 0) {
            return false;
        }

        try {
            Advisor advisorEntity = advisorProfileRepository.findProfileById(advisorId);
            boolean exists = advisorEntity != null;
            log.debug("Advisor with ID {} exists: {}", advisorId, exists);
            return exists;
        } catch (Exception e) {
            log.error("Error checking advisor existence for ID {}: {}", advisorId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Enriches the advisor profile with company information using modern CompanyRepository.
     * This method replaces the problematic EJB CompanyFacade with a clean JDBC Template implementation.
     *
     * Key improvements over the original CompanyFacade:
     * 1. Fixed parameter name bug (contactId vs contactIntId)
     * 2. Better error handling and resource management
     * 3. Single query approach to avoid N+1 problems
     * 4. Proper logging and debugging information
     *
     * @param advisorProfile the advisor profile DTO to enrich
     * @param advisorEntity the advisor entity containing the contact ID
     */
    private void enrichAdvisorProfileWithCompanies(AdvisorProfileDTO advisorProfile, Advisor advisorEntity) {
        log.debug("Enriching advisor profile with company information for ID: {}", advisorProfile.getAdvisorId());

        try {
            // Check if advisor has contact information
            if (advisorEntity.getContact() != null && advisorEntity.getContact().getContactIntId() != null) {
                Integer contactId = advisorEntity.getContact().getContactIntId();

                log.debug("Fetching companies for contact ID: {}", contactId);

                // Use our modern CompanyRepository instead of the buggy EJB Facade
                List<Company> companies = companyRepository.findCompaniesByContactId(contactId);

                if (companies != null && !companies.isEmpty()) {
                    // Convert Company entities to CompanyDTOs using mapper
                    List<com.insurfact.ins.dto.CompanyDTO> companyDTOs = companies.stream()
                        .map(advisorMapper::toCompanyDTO)
                        .collect(java.util.stream.Collectors.toList());

                    // Sort companies to put primary company first
                    companyDTOs.sort((c1, c2) -> {
                        if (Boolean.TRUE.equals(c1.getIsPrimary())) return -1;
                        if (Boolean.TRUE.equals(c2.getIsPrimary())) return 1;
                        return 0;
                    });

                    advisorProfile.setCompanies(companyDTOs);
                    log.info("Successfully enriched advisor {} with {} companies",
                        advisorProfile.getAdvisorId(), companyDTOs.size());
                } else {
                    advisorProfile.setCompanies(new java.util.ArrayList<>());
                    log.debug("No companies found for advisor ID: {}", advisorProfile.getAdvisorId());
                }
            } else {
                advisorProfile.setCompanies(new java.util.ArrayList<>());
                log.debug("No contact information available for advisor ID: {}", advisorProfile.getAdvisorId());
            }
        } catch (Exception e) {
            log.error("Error enriching advisor profile with companies for ID {}: {}",
                advisorProfile.getAdvisorId(), e.getMessage(), e);
            // Set empty list on error to prevent null pointer exceptions
            advisorProfile.setCompanies(new java.util.ArrayList<>());
        }
    }
}
